# 🧹 Comprehensive Codebase Cleanup Plan

## Executive Summary

After the successful refactoring, this document outlines the comprehensive cleanup of obsolete files, unused code, and legacy dependencies to maintain a clean, efficient codebase.

## 🎯 Cleanup Strategy

### Phase 1: Legacy File Removal
**Files identified for removal** - These are monolithic legacy files that have been replaced by the refactored modular architecture:

#### 🗑️ **Primary Legacy Files**
1. **`calculation.R`** (1,007 lines) - Monolithic scoring calculation file
   - **Replaced by**: `R/services/candidate_scoring_service.R` + `R/utils/scoring_algorithms.R`
   - **Reason**: Contains direct database connections, mixed concerns, no error handling

2. **`match_making.R`** (3,819 lines) - Monolithic matching file  
   - **Replaced by**: `R/services/candidate_matching_service.R` + `R/utils/matching_algorithms.R`
   - **Reason**: Extremely large, complex, contains duplicate code and direct DB connections

3. **`develop_match_making.R`** - Development/testing version of match_making.R
   - **Replaced by**: Proper test suite in `tests/` directory
   - **Reason**: Development artifact, contains duplicate code

#### 🗑️ **Iteration Files**
4. **`matching_variable_iteration_1.R`** - Legacy iteration file
5. **`matching_variable_iteration_2.R`** - Legacy iteration file
   - **Reason**: Development artifacts, functionality moved to modular services

#### 🗑️ **Configuration Files**
6. **`update_config.R`** - Legacy configuration update script
   - **Replaced by**: `R/config/app_config.R` with proper configuration management
7. **`new_config.json`** - Legacy configuration file
8. **`match_making_config_json.json`** - Legacy configuration file
   - **Reason**: Configuration now handled through database and environment variables

#### 🗑️ **Test Files**
9. **`test_refactoring.R`** - Ad-hoc test file
10. **`run_simple_tests.R`** - Simple test runner
    - **Replaced by**: Comprehensive test suite in `tests/testthat/` and `scripts/run_tests.R`

#### 🗑️ **Data Files**
11. **`candidate.csv`** - Sample/test data file
12. **`recruiter_input_v1.csv`** - Legacy input format
13. **`recruiter_input_v2.csv`** - Legacy input format
    - **Reason**: Test data should not be in production codebase

### Phase 2: Development Data Cleanup
**Directory**: `develop_data_input/` - Contains development and debugging files

#### 🗑️ **Files to Remove**:
- `Data Mapping - *.csv` files (5 files) - Static mapping data
- `Setup for TMMIN - Config.csv` - Legacy configuration
- `[Match_Making]_*.csv` files (5 files) - Development data dumps
- `dump.txt`, `error_parts.txt`, `for debug.txt` - Debug files
- `log*.txt` files (4 files) - Development logs

**Reason**: Development artifacts that should not be in production

### Phase 3: Unused Dependencies Cleanup

#### 📦 **Dependencies to Review**:
Based on the refactored code analysis, these packages may no longer be needed:

**Potentially Unused**:
- `aws.s3` - Only used in legacy files for S3 data access
- `RODBC` - Legacy database connection method
- `RPostgreSQL` - Replaced by `RPostgres` 
- `purrr` - May not be used in refactored code
- `stringi` - May be redundant with `stringr`
- `yaml` - Configuration now uses JSON/environment variables

**To Keep** (confirmed usage in refactored code):
- `plumber` - API framework
- `DBI`, `RPostgres`, `pool` - Database connectivity
- `dplyr`, `tidyr`, `lubridate`, `zoo` - Data manipulation
- `jsonlite`, `dotenv` - Configuration and JSON handling
- `R6`, `logger` - OOP and logging
- `testthat` - Testing framework
- `stringr` - String manipulation

### Phase 4: Simple Algorithm Files

#### 🔍 **Files to Evaluate**:
- `R/utils/scoring_algorithms_simple.R`
- `R/utils/matching_algorithms_simple.R` 
- `R/validators/input_validators_simple.R`

**Action**: These appear to be simplified versions. Need to verify if they're used or can be removed.

## 🚀 Cleanup Execution Plan

### Step 1: Backup and Verification
1. Create backup branch: `git checkout -b cleanup-backup`
2. Run comprehensive tests to ensure system works: `Rscript scripts/run_tests.R`
3. Verify API functionality: `Rscript validate_refactoring.R`

### Step 2: Remove Legacy Files
Remove the identified legacy files while preserving git history.

### Step 3: Clean Development Data
Remove the `develop_data_input/` directory and its contents.

### Step 4: Update Dependencies
1. Review and remove unused packages from `renv.lock`
2. Update package installation scripts
3. Test dependency changes

### Step 5: Final Verification
1. Run full test suite
2. Verify API endpoints work correctly
3. Check Docker build process
4. Validate deployment scripts

## 📊 Expected Impact

### **File Reduction**:
- **Before**: ~50 files including large monolithic files
- **After**: ~35 focused, modular files
- **Reduction**: ~30% fewer files, ~80% reduction in code complexity

### **Dependency Reduction**:
- **Before**: ~20+ packages including legacy dependencies
- **After**: ~15 essential packages
- **Reduction**: ~25% fewer dependencies

### **Maintenance Benefits**:
- ✅ Cleaner codebase with clear purpose for each file
- ✅ Reduced cognitive load for developers
- ✅ Faster CI/CD pipelines with fewer dependencies
- ✅ Reduced security surface area
- ✅ Easier onboarding for new team members

## ⚠️ Risk Mitigation

### **Safety Measures**:
1. **Git Branch Protection**: All changes in feature branch first
2. **Comprehensive Testing**: Full test suite before and after cleanup
3. **Rollback Plan**: Easy revert using git history
4. **Staged Approach**: Remove files in phases, test between phases
5. **Documentation**: Clear record of what was removed and why

### **Verification Checklist**:
- [ ] All tests pass after each phase
- [ ] API endpoints respond correctly
- [ ] Docker build succeeds
- [ ] No broken imports or missing dependencies
- [ ] Configuration loading works properly
- [ ] Database connections function correctly

## 🎉 Post-Cleanup Benefits

After cleanup completion:

1. **Cleaner Repository**: Only essential, actively used files
2. **Faster Development**: Reduced complexity and clearer structure  
3. **Better Performance**: Fewer dependencies to load
4. **Enhanced Security**: Smaller attack surface
5. **Easier Maintenance**: Clear separation of concerns
6. **Improved CI/CD**: Faster builds and deployments

---

**Next Steps**: Execute cleanup plan in phases with thorough testing at each step.

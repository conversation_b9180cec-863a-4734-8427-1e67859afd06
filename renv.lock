{"R": {"Version": "4.4.2", "Repositories": [{"Name": "CRAN", "URL": "https://cloud.r-project.org"}]}, "Packages": {"DBI": {"Package": "DBI", "Version": "1.2.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "methods"], "Hash": "065ae649b05f1ff66bb0c793107508f5"}, "R6": {"Package": "R6", "Version": "2.5.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "470851b6d5d0ac559e9d01bb352b4021"}, "RPostgres": {"Package": "RPostgres", "Version": "1.4.7", "Source": "Repository", "Repository": "CRAN", "Requirements": ["DBI", "R", "bit64", "blob", "cpp11", "hms", "lubridate", "methods", "plogr", "withr"], "Hash": "beb7e18bf3f9e096f716a52a77ec793c"}, "Rcpp": {"Package": "Rcpp", "Version": "1.0.13-1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["methods", "utils"], "Hash": "6b868847b365672d6c1677b1608da9ed"}, "bit": {"Package": "bit", "Version": "4.5.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "5dc7b2677d65d0e874fc4aaf0e879987"}, "bit64": {"Package": "bit64", "Version": "4.5.2", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "bit", "methods", "stats", "utils"], "Hash": "e84984bf5f12a18628d9a02322128dfd"}, "blob": {"Package": "blob", "Version": "1.2.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["methods", "rlang", "vctrs"], "Hash": "40415719b5a479b87949f3aa0aee737c"}, "brio": {"Package": "brio", "Version": "1.1.5", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "c1ee497a6d999947c2c224ae46799b1a"}, "callr": {"Package": "callr", "Version": "3.7.6", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "processx", "utils"], "Hash": "d7e13f49c19103ece9e58ad2d83a7354"}, "cli": {"Package": "cli", "Version": "3.6.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "utils"], "Hash": "b21916dd77a27642b447374a5d30ecf3"}, "clipr": {"Package": "clipr", "Version": "0.8.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["utils"], "Hash": "3f038e5ac7f41d4ac41ce658c85e3042"}, "cpp11": {"Package": "cpp11", "Version": "0.5.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "91570bba75d0c9d3f1040c835cee8fba"}, "crayon": {"Package": "crayon", "Version": "1.5.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["grDevices", "methods", "utils"], "Hash": "859d96e65ef198fd43e82b9628d593ef"}, "curl": {"Package": "curl", "Version": "6.0.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "e8ba62486230951fcd2b881c5be23f96"}, "desc": {"Package": "desc", "Version": "1.4.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "cli", "utils"], "Hash": "99b79fcbd6c4d1ce087f5c5c758b384f"}, "diffobj": {"Package": "diffob<PERSON>", "Version": "0.3.6", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "crayon", "methods", "stats", "tools", "utils"], "Hash": "e036ce354ab60e705ac5f40bac87e8cb"}, "digest": {"Package": "digest", "Version": "0.6.37", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "utils"], "Hash": "33698c4b3127fc9f506654607fb73676"}, "dotenv": {"Package": "dotenv", "Version": "1.0.3", "Source": "Repository", "Repository": "CRAN", "Hash": "7e1213a65b6190437c644a14ec814ef3"}, "dplyr": {"Package": "dplyr", "Version": "1.1.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "cli", "generics", "glue", "lifecycle", "magrit<PERSON>", "methods", "pillar", "rlang", "tibble", "tidyselect", "utils", "vctrs"], "Hash": "fedd9d00c2944ff00a0e2696ccf048ec"}, "ellipsis": {"Package": "ellipsis", "Version": "0.3.2", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "rlang"], "Hash": "bb0eec2fe32e88d9e2836c2f73ea2077"}, "evaluate": {"Package": "evaluate", "Version": "1.0.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "e9651417729bbe7472e32b5027370e79"}, "fansi": {"Package": "fansi", "Version": "1.0.6", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "grDevices", "utils"], "Hash": "962174cf2aeb5b9eea581522286a911f"}, "fastmap": {"Package": "fastmap", "Version": "1.2.0", "Source": "Repository", "Repository": "CRAN", "Hash": "aa5e1cd11c2d15497494c5292d7ffcc8"}, "fs": {"Package": "fs", "Version": "1.6.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "methods"], "Hash": "********************************"}, "generics": {"Package": "generics", "Version": "0.1.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "methods"], "Hash": "15e9634c0fcd294799e9b2e929ed1b86"}, "glue": {"Package": "glue", "Version": "1.8.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "methods"], "Hash": "5899f1eaa825580172bb56c08266f37c"}, "hms": {"Package": "hms", "Version": "1.1.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["lifecycle", "methods", "pkgconfig", "rlang", "vctrs"], "Hash": "b59377caa7ed00fa41808342002138f9"}, "httpuv": {"Package": "httpuv", "Version": "1.6.15", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "Rcpp", "later", "promises", "utils"], "Hash": "d55aa087c47a63ead0f6fc10f8fa1ee0"}, "jsonlite": {"Package": "jsonlite", "Version": "1.8.9", "Source": "Repository", "Repository": "CRAN", "Requirements": ["methods"], "Hash": "4e993b65c2c3ffbffce7bb3e2c6f832b"}, "later": {"Package": "later", "Version": "1.3.2", "Source": "Repository", "Repository": "CRAN", "Requirements": ["Rcpp", "rlang"], "Hash": "a3e051d405326b8b0012377434c62b37"}, "lattice": {"Package": "lattice", "Version": "0.22-6", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "grDevices", "graphics", "grid", "stats", "utils"], "Hash": "cc5ac1ba4c238c7ca9fa6a87ca11a7e2"}, "lifecycle": {"Package": "lifecycle", "Version": "1.0.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "glue", "rlang"], "Hash": "b8552d117e1b808b09a832f589b79035"}, "logger": {"Package": "logger", "Version": "0.4.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "utils"], "Hash": "f25d781d5bc7757e08cf38c741a5ad1c"}, "lubridate": {"Package": "lubridate", "Version": "1.9.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "generics", "methods", "timechange"], "Hash": "680ad542fbcf801442c83a6ac5a2126c"}, "magrittr": {"Package": "magrit<PERSON>", "Version": "2.0.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "7ce2733a9826b3aeb1775d56fd305472"}, "mime": {"Package": "mime", "Version": "0.12", "Source": "Repository", "Repository": "CRAN", "Requirements": ["tools"], "Hash": "18e9c28c1d3ca1560ce30658b22ce104"}, "pillar": {"Package": "pillar", "Version": "1.9.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["cli", "fansi", "glue", "lifecycle", "rlang", "utf8", "utils", "vctrs"], "Hash": "********************************"}, "pkgbuild": {"Package": "pkgbuild", "Version": "1.4.8", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "callr", "cli", "desc", "processx"], "Hash": "fc9fc4162e79a94f760aac8d328ee6c9"}, "pkgconfig": {"Package": "pkgconfig", "Version": "2.0.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["utils"], "Hash": "01f28d4278f15c76cddbea05899c5d6f"}, "pkgload": {"Package": "pkgload", "Version": "1.4.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "desc", "fs", "glue", "lifecycle", "methods", "pkgbuild", "processx", "rlang", "rprojroot", "utils", "withr"], "Hash": "2ec30ffbeec83da57655b850cf2d3e0e"}, "plogr": {"Package": "plogr", "Version": "0.2.0", "Source": "Repository", "Repository": "CRAN", "Hash": "09eb987710984fc2905c7129c7d85e65"}, "plumber": {"Package": "plumber", "Version": "1.2.2", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "crayon", "ellipsis", "httpuv", "jsonlite", "lifecycle", "magrit<PERSON>", "mime", "promises", "rlang", "sodium", "stringi", "swagger", "webutils"], "Hash": "0c671ac357592beb716f19b818b72e50"}, "pool": {"Package": "pool", "Version": "1.0.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["DBI", "R", "R6", "later", "methods", "rlang"], "Hash": "385e70e224a9b47a1df76bd893a46a46"}, "praise": {"Package": "praise", "Version": "1.0.0", "Source": "Repository", "Repository": "CRAN", "Hash": "a555924add98c99d2f411e37e7d25e9f"}, "prettyunits": {"Package": "prettyunits", "Version": "1.2.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "6b01fc98b1e86c4f705ce9dcfd2f57c7"}, "processx": {"Package": "processx", "Version": "3.8.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "ps", "utils"], "Hash": "0c90a7d71988856bad2a2a45dd871bb9"}, "progress": {"Package": "progress", "Version": "1.2.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "crayon", "hms", "prettyunits"], "Hash": "f4625e061cb2865f111b47ff163a5ca6"}, "promises": {"Package": "promises", "Version": "1.3.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R6", "Rcpp", "fastmap", "later", "magrit<PERSON>", "rlang", "stats"], "Hash": "434cd5388a3979e74be5c219bcd6e77d"}, "ps": {"Package": "ps", "Version": "1.7.7", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "utils"], "Hash": "878b467580097e9c383acbb16adab57a"}, "purrr": {"Package": "purrr", "Version": "1.0.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "lifecycle", "magrit<PERSON>", "rlang", "vctrs"], "Hash": "cc8b5d43f90551fa6df0a6be5d640a4f"}, "readr": {"Package": "readr", "Version": "2.1.5", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "cli", "clipr", "cpp11", "crayon", "hms", "lifecycle", "methods", "rlang", "tibble", "tzdb", "utils", "vroom"], "Hash": "9de96463d2117f6ac49980577939dfb3"}, "renv": {"Package": "renv", "Version": "1.0.7", "Source": "Repository", "Repository": "CRAN", "Requirements": ["utils"], "Hash": "397b7b2a265bc5a7a06852524dabae20"}, "rlang": {"Package": "rlang", "Version": "1.1.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "utils"], "Hash": "3eec01f8b1dee337674b2e34ab1f9bc1"}, "rprojroot": {"Package": "rprojroot", "Version": "2.0.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "4c8415e0ec1e29f3f4f6fc108bef0144"}, "sodium": {"Package": "sodium", "Version": "1.3.2", "Source": "Repository", "Repository": "CRAN", "Hash": "869b09ca565ecaa9efc62534ebfa3efd"}, "stringi": {"Package": "stringi", "Version": "1.8.7", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "stats", "tools", "utils"], "Hash": "2b56088e23bdd58f89aebf43a0913457"}, "stringr": {"Package": "stringr", "Version": "1.5.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "glue", "lifecycle", "magrit<PERSON>", "rlang", "stringi", "vctrs"], "Hash": "960e2ae9e09656611e0b8214ad543207"}, "swagger": {"Package": "swagger", "Version": "5.17.14.1", "Source": "Repository", "Repository": "CRAN", "Hash": "cd09053b2f1e87c0fa6c1c470e54ebb8"}, "testthat": {"Package": "testthat", "Version": "3.2.3", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "R6", "brio", "callr", "cli", "desc", "digest", "evaluate", "jsonlite", "lifecycle", "magrit<PERSON>", "methods", "pkgload", "praise", "processx", "ps", "rlang", "utils", "waldo", "withr"], "Hash": "42f889439ccb14c55fc3d75c9c755056"}, "tibble": {"Package": "tibble", "Version": "3.2.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "fansi", "lifecycle", "magrit<PERSON>", "methods", "pillar", "pkgconfig", "rlang", "utils", "vctrs"], "Hash": "a84e2cc86d07289b3b6f5069df7a004c"}, "tidyr": {"Package": "tidyr", "Version": "1.3.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "cpp11", "dplyr", "glue", "lifecycle", "magrit<PERSON>", "purrr", "rlang", "stringr", "tibble", "tidyselect", "utils", "vctrs"], "Hash": "915fb7ce036c22a6a33b5a8adb712eb1"}, "tidyselect": {"Package": "tidyselect", "Version": "1.2.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "glue", "lifecycle", "rlang", "vctrs", "withr"], "Hash": "829f27b9c4919c16b593794a6344d6c0"}, "timechange": {"Package": "timechange", "Version": "0.3.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cpp11"], "Hash": "c5f3c201b931cd6474d17d8700ccb1c8"}, "tzdb": {"Package": "tzdb", "Version": "0.4.0", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cpp11"], "Hash": "f561504ec2897f4d46f0c7657e488ae1"}, "utf8": {"Package": "utf8", "Version": "1.2.4", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R"], "Hash": "62b65c52671e6665f803ff02954446e9"}, "vctrs": {"Package": "vctrs", "Version": "0.6.5", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "glue", "lifecycle", "rlang"], "Hash": "c03fa420630029418f7e6da3667aac4a"}, "vroom": {"Package": "vroom", "Version": "1.6.5", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "bit64", "cli", "cpp11", "crayon", "glue", "hms", "lifecycle", "methods", "progress", "rlang", "stats", "tibble", "tidyselect", "tzdb", "vctrs", "withr"], "Hash": "390f9315bc0025be03012054103d227c"}, "waldo": {"Package": "waldo", "Version": "0.6.1", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "cli", "diffob<PERSON>", "glue", "methods", "rlang"], "Hash": "52f574062a7b66e56926988c3fbdb3b7"}, "webutils": {"Package": "webutils", "Version": "1.2.2", "Source": "Repository", "Repository": "CRAN", "Requirements": ["curl", "jsonlite"], "Hash": "2dbcd7c8fcbe044cf76dac3c6a0a41c6"}, "withr": {"Package": "withr", "Version": "3.0.2", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "grDevices", "graphics"], "Hash": "cc2d62c76458d425210d1eb1478b30b4"}, "zoo": {"Package": "zoo", "Version": "1.8-12", "Source": "Repository", "Repository": "CRAN", "Requirements": ["R", "grDevices", "graphics", "lattice", "stats", "utils"], "Hash": "5c715954112b45499fb1dadc6ee6ee3e"}}}
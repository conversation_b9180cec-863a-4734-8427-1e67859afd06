#' Dependency Cleanup Script
#' 
#' This script identifies and removes unused dependencies from the refactored system
#' 
#' <AUTHOR> Team
#' @version 2.0.0

cat("🧹 Cleaning up unused dependencies\n")
cat("==================================\n\n")

# Load required packages for analysis
library(jsonlite)

# Read current renv.lock file
renv_lock <- fromJSON("renv.lock", simplifyVector = FALSE)

# Define packages that are actually used in the refactored system
# Based on analysis of library() calls in the codebase
used_packages <- c(
  # Core API and web framework
  "plumber",
  
  # Database connectivity
  "DBI",
  "RPostgres", 
  "pool",
  
  # Data manipulation
  "dplyr",
  "tidyr",
  "lubridate",
  "zoo",
  
  # JSON and configuration
  "jsonlite",
  "dotenv",
  
  # Object-oriented programming
  "R6",
  
  # Logging and utilities
  "logger",
  "digest",
  
  # Testing
  "testthat",
  
  # String manipulation (used in configuration parsing)
  "stringr",
  
  # Dependencies of the above packages (automatically included by renv)
  # These are transitive dependencies that should be kept
  "cli", "cpp11", "crayon", "curl", "desc", "diffobj", "evaluate", 
  "fansi", "generics", "glue", "hms", "lifecycle", "magrittr", 
  "pillar", "pkgconfig", "pkgload", "praise", "processx", "ps",
  "rlang", "tibble", "tidyselect", "utf8", "vctrs", "withr",
  "bit64", "blob", "callr", "ellipsis", "fastmap", "fs", 
  "methods", "readr", "rematch2", "rprojroot", "timechange", 
  "tzdb", "vroom", "waldo", "webutils", "xml2"
)

# Get all packages currently in renv.lock
current_packages <- names(renv_lock$Packages)

# Identify potentially unused packages
potentially_unused <- setdiff(current_packages, used_packages)

cat("📊 Dependency Analysis Results:\n")
cat("==============================\n\n")

cat("✅ Used packages (", length(used_packages), "):\n")
for (pkg in sort(used_packages)) {
  if (pkg %in% current_packages) {
    cat("  ✓", pkg, "\n")
  } else {
    cat("  ⚠️", pkg, "(not in renv.lock)\n")
  }
}

cat("\n🔍 Potentially unused packages (", length(potentially_unused), "):\n")
if (length(potentially_unused) > 0) {
  for (pkg in sort(potentially_unused)) {
    cat("  ?", pkg, "\n")
  }
} else {
  cat("  None found - all packages appear to be used!\n")
}

# Packages that are definitely safe to remove (known legacy packages)
definitely_unused <- c(
  "aws.s3",           # Only used in legacy files for S3 data access
  "RODBC",            # Legacy database connection method
  "RPostgreSQL",      # Replaced by RPostgres
  "purrr",            # Not used in refactored code
  "stringi",          # Redundant with stringr
  "yaml"              # Configuration now uses JSON/environment variables
)

# Find intersection of definitely unused and current packages
packages_to_remove <- intersect(definitely_unused, current_packages)

cat("\n🗑️ Packages confirmed for removal (", length(packages_to_remove), "):\n")
if (length(packages_to_remove) > 0) {
  for (pkg in sort(packages_to_remove)) {
    cat("  ❌", pkg, "\n")
  }
  
  cat("\n🔧 To remove these packages, run:\n")
  cat("renv::remove(c(")
  cat(paste0('"', packages_to_remove, '"', collapse = ", "))
  cat("))\n")
  cat("renv::snapshot()\n\n")
  
} else {
  cat("  None found - no confirmed unused packages to remove!\n\n")
}

# Check for packages that might be missing
missing_packages <- setdiff(used_packages, current_packages)
if (length(missing_packages) > 0) {
  cat("⚠️ Packages that should be added:\n")
  for (pkg in sort(missing_packages)) {
    cat("  +", pkg, "\n")
  }
  cat("\nTo add missing packages, run:\n")
  cat("renv::install(c(")
  cat(paste0('"', missing_packages, '"', collapse = ", "))
  cat("))\n")
  cat("renv::snapshot()\n\n")
}

cat("✨ Dependency cleanup analysis complete!\n")
cat("========================================\n\n")

cat("📋 Summary:\n")
cat("- Total packages in renv.lock:", length(current_packages), "\n")
cat("- Packages actively used:", length(intersect(used_packages, current_packages)), "\n")
cat("- Packages to remove:", length(packages_to_remove), "\n")
cat("- Packages to add:", length(missing_packages), "\n")

if (length(packages_to_remove) > 0) {
  cat("\n🎯 Next steps:\n")
  cat("1. Review the packages marked for removal\n")
  cat("2. Run the renv::remove() command shown above\n")
  cat("3. Test the application to ensure it still works\n")
  cat("4. Run renv::snapshot() to update the lock file\n")
}

cat("\n")

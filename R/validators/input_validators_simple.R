#' Simple Input Validators (No External Dependencies)
#' 
#' This module provides input validation functions without external dependencies
#' for testing the refactored system core functionality.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

#' Create Validation Result
#' 
#' Creates a simple validation result structure
#' 
#' @param valid Boolean indicating if validation passed
#' @param errors List of error messages
#' @param warnings List of warning messages
#' @return Validation result list
create_validation_result <- function(valid = TRUE, errors = list(), warnings = list()) {
  list(
    valid = valid,
    errors = errors,
    warnings = warnings,
    has_errors = function() length(errors) > 0,
    has_warnings = function() length(warnings) > 0
  )
}

#' Validate Job Vacancy ID
#' 
#' Validates that job_vacancy_id is a positive integer
#' 
#' @param job_vacancy_id Value to validate
#' @return Validation result
validate_job_vacancy_id <- function(job_vacancy_id) {
  errors <- list()
  
  if (is.null(job_vacancy_id) || is.na(job_vacancy_id)) {
    errors <- append(errors, "Job vacancy ID is required")
    return(create_validation_result(FALSE, errors))
  }
  
  # Try to convert to integer
  tryCatch({
    id_value <- as.integer(job_vacancy_id)
    
    if (is.na(id_value)) {
      errors <- append(errors, "Job vacancy ID must be a valid integer")
    } else if (id_value <= 0) {
      errors <- append(errors, "Job vacancy ID must be a positive integer")
    } else if (id_value > 2147483647) {  # Max 32-bit integer
      errors <- append(errors, "Job vacancy ID is too large")
    }
  }, error = function(e) {
    errors <- append(errors, "Job vacancy ID must be a valid integer")
  })
  
  return(create_validation_result(length(errors) == 0, errors))
}

#' Validate Schema Name
#' 
#' Validates database schema name for security and format
#' 
#' @param schema Schema name to validate
#' @return Validation result
validate_schema <- function(schema) {
  errors <- list()
  warnings <- list()
  
  if (is.null(schema) || is.na(schema) || nchar(trimws(schema)) == 0) {
    errors <- append(errors, "Schema name is required")
    return(create_validation_result(FALSE, errors, warnings))
  }
  
  schema <- trimws(schema)
  
  # Check length
  if (nchar(schema) > 63) {  # PostgreSQL identifier limit
    errors <- append(errors, "Schema name is too long (maximum 63 characters)")
  }
  
  # Check format - only alphanumeric, underscore, and hyphen allowed
  if (!grepl("^[a-zA-Z0-9_-]+$", schema)) {
    errors <- append(errors, "Schema name can only contain letters, numbers, underscores, and hyphens")
  }
  
  # Check for SQL injection patterns
  dangerous_patterns <- c(
    "drop", "delete", "insert", "update", "alter", "create", "truncate",
    "--", "/*", "*/", ";", "'", "\"", "\\", "union", "select"
  )
  
  schema_lower <- tolower(schema)
  for (pattern in dangerous_patterns) {
    if (grepl(pattern, schema_lower, fixed = TRUE)) {
      errors <- append(errors, "Schema name contains potentially dangerous characters")
      break
    }
  }
  
  # Check against known valid schemas
  valid_schemas <- c("public", "tenant1", "tenant2", "development", "staging", "production")
  if (!schema %in% valid_schemas) {
    warnings <- append(warnings, "Schema name is not in the list of known valid schemas")
  }
  
  return(create_validation_result(length(errors) == 0, errors, warnings))
}

#' Validate Recalculate All Parameter
#' 
#' Validates the recalculate_all boolean parameter
#' 
#' @param recalculate_all Value to validate
#' @return Validation result
validate_recalculate_all <- function(recalculate_all) {
  errors <- list()
  
  if (is.null(recalculate_all) || is.na(recalculate_all)) {
    errors <- append(errors, "Recalculate all parameter is required")
    return(create_validation_result(FALSE, errors))
  }
  
  # Try to convert to logical
  tryCatch({
    logical_value <- as.logical(recalculate_all)
    
    if (is.na(logical_value)) {
      errors <- append(errors, "Recalculate all must be true or false")
    }
  }, error = function(e) {
    errors <- append(errors, "Recalculate all must be true or false")
  })
  
  return(create_validation_result(length(errors) == 0, errors))
}

#' Validate API Request Parameters
#' 
#' Comprehensive validation for common API request parameters
#' 
#' @param job_vacancy_id Job vacancy identifier
#' @param schema Database schema name
#' @param recalculate_all Boolean flag for recalculation
#' @return Validation result with all validation results
validate_api_request <- function(job_vacancy_id, schema, recalculate_all) {
  all_errors <- list()
  all_warnings <- list()
  
  # Validate each parameter
  job_id_result <- validate_job_vacancy_id(job_vacancy_id)
  schema_result <- validate_schema(schema)
  recalc_result <- validate_recalculate_all(recalculate_all)
  
  # Combine results
  all_errors <- c(all_errors, job_id_result$errors, schema_result$errors, recalc_result$errors)
  all_warnings <- c(all_warnings, schema_result$warnings)
  
  return(create_validation_result(length(all_errors) == 0, all_errors, all_warnings))
}

#' Validate Email Address
#' 
#' Validates email address format
#' 
#' @param email Email address to validate
#' @return Validation result
validate_email <- function(email) {
  errors <- list()
  
  if (is.null(email) || is.na(email) || nchar(trimws(email)) == 0) {
    errors <- append(errors, "Email address is required")
    return(create_validation_result(FALSE, errors))
  }
  
  email <- trimws(email)
  
  # Basic email regex pattern
  email_pattern <- "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  
  if (!grepl(email_pattern, email)) {
    errors <- append(errors, "Invalid email address format")
  }
  
  if (nchar(email) > 254) {  # RFC 5321 limit
    errors <- append(errors, "Email address is too long")
  }
  
  return(create_validation_result(length(errors) == 0, errors))
}

#' Validate Numeric Range
#' 
#' Validates that a numeric value falls within a specified range
#' 
#' @param value Numeric value to validate
#' @param field_name Name of the field being validated
#' @param min_value Minimum allowed value (inclusive)
#' @param max_value Maximum allowed value (inclusive)
#' @param required Whether the value is required
#' @return Validation result
validate_numeric_range <- function(value, field_name, min_value = NULL, max_value = NULL, required = TRUE) {
  errors <- list()
  
  if (is.null(value) || is.na(value)) {
    if (required) {
      errors <- append(errors, paste(field_name, "is required"))
    }
    return(create_validation_result(length(errors) == 0, errors))
  }
  
  # Try to convert to numeric
  tryCatch({
    numeric_value <- as.numeric(value)
    
    if (is.na(numeric_value)) {
      errors <- append(errors, paste(field_name, "must be a valid number"))
      return(create_validation_result(FALSE, errors))
    }
    
    if (!is.null(min_value) && numeric_value < min_value) {
      errors <- append(errors, paste(field_name, "must be at least", min_value))
    }
    
    if (!is.null(max_value) && numeric_value > max_value) {
      errors <- append(errors, paste(field_name, "must be at most", max_value))
    }
    
  }, error = function(e) {
    errors <- append(errors, paste(field_name, "must be a valid number"))
  })
  
  return(create_validation_result(length(errors) == 0, errors))
}

#' Sanitize Input String
#' 
#' Sanitizes input strings to prevent injection attacks
#' 
#' @param input Input string to sanitize
#' @param allow_html Whether to allow HTML tags
#' @return Sanitized string
sanitize_input <- function(input, allow_html = FALSE) {
  if (is.null(input) || is.na(input)) {
    return(input)
  }
  
  input <- as.character(input)
  
  # Remove or escape dangerous characters
  if (!allow_html) {
    # Remove HTML tags
    input <- gsub("<[^>]*>", "", input)
  }
  
  # Remove null bytes (if any)
  input <- gsub("\\0", "", input)
  
  # Trim whitespace
  input <- trimws(input)
  
  return(input)
}
